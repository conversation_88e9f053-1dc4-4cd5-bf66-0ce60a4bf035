<table class="table table-hover table-bordered table-vertical-center">
    <thead>
        <tr>
            <th>Line Item</th>
            <th>Total Budget</th>
            <th>Completed Renovations</th>
            <th>Previously Disbursed</th>
            <th>% Completed</th>
            <th style="width: 70px;">% Requested</th>
            <th style="width: 70px;">Disbursement This Draw</th>
            <th style="width: 70px;">Borrower Notes</th>
            <th style="width: 70px;">Lender Notes</th>
            <th style="width: 200px;" class="hide col-reject-reason">Reject Reason</th>
        </tr>
    </thead>
    <tbody>
        <?php if (!empty($categoriesData)): ?>
            <?php foreach ($categoriesData as $category): ?>
                <?php if (!empty($category['lineItems'])): ?>
                    <tr class="category-header">
                        <td colspan="11">
                            <?= htmlspecialchars(strtoupper($category['name'])) ?>
                            <?php if (!empty($category['description'])): ?>
                            <i class="fa fa-info-circle text-primary tooltipClass ml-2"
                                title="<?= htmlspecialchars($category['description']) ?>"></i>
                            <?php endif; ?>
                        </td>
                    </tr>
                    <?php if (!empty($category['lineItems'])): ?>
                        <?php foreach ($category['lineItems'] as $lineItem): ?>
                            <tr class="line-item">
                                <td>
                                    <?= htmlspecialchars($lineItem['name']) ?>
                                    <?php if (!empty($lineItem['description'])): ?>
                                        <i class="fa fa-info-circle text-primary tooltipClass ml-2" title="<?= htmlspecialchars($lineItem['description']) ?>"></i>
                                    <?php endif; ?>
                                </td>
                                <td>$<?= $lineItem['cost'] ?></td>
                                <td>$<?= $lineItem['completedAmount'] ?></td>
                                <td>0</td>
                                <td>
                                    <span class="percentage"><?= round($lineItem['completedPercent']) ?>%</span>
                                </td>
                                <td>
                                    0
                                </td>
                                <td>
                                    0
                                </td>
                                <td>
                                    <button class="btn note-btn btn-sm" type="button">
                                        <i class="icon-md fas fa-comment-medical fa-lg tooltipClass <?= !empty($lineItem['notes']) ? 'text-primary' : 'text-muted' ?>"
                                            data-original-title="<?= htmlspecialchars($lineItem['notes']) ?>">
                                        </i>
                                    </button>
                                </td>
                                <td>
                                    <button class="btn lender-note-btn btn-sm" type="button"
                                        data-line-item-id="<?= $lineItem['id'] ?>">
                                        <i class="icon-md fas fa-comment-medical fa-lg tooltipClass <?= !empty($lineItem['lenderNotes']) ? 'text-primary' : 'text-muted' ?>"
                                            data-original-title="<?= htmlspecialchars($lineItem['lenderNotes']) ?>">
                                        </i>
                                    </button>
                                    <input type="hidden" name="lenderNotes" value="<?= htmlspecialchars($lineItem['lenderNotes']) ?>">
                                </td>
                                <td class="hide col-reject-reason">
                                    <select class="form-control input-sm" name="rejectReason">
                                        <option <?= $lineItem['rejectReason'] === '' ? 'selected' : ''; ?> value="">-- None --</option>
                                        <option <?= $lineItem['rejectReason'] === 'Revise Budget' ? 'selected' : ''; ?> value="Revise Budget">Revise Budget</option>
                                        <option <?= $lineItem['rejectReason'] === 'Line Item not covered' ? 'selected' : ''; ?> value="Line Item not covered">Line Item not covered</option>
                                        <option <?= $lineItem['rejectReason'] === 'Other(See Notes)' ? 'selected' : ''; ?> value="Other(See Notes)">Other(See Notes)</option>
                                    </select>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                <?php endif; ?>
            <?php endforeach; ?>
        <?php else: ?>
        <tr>
            <td colspan="11" class="text-center text-muted py-4">
                No draw request data available.
            </td>
        </tr>
        <?php endif; ?>
    </tbody>
</table>
