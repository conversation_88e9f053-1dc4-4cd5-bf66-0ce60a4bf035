/**
 * Borrower-specific Draw Management Functionality
 */

DrawManagement.borrower = {

    /**
     * Initialize borrower-specific functionality
     */
    init: function() {
        DrawManagement.config.lmrid = $('#lmrid').val();
        DrawManagement.config.dataKey = 'lmrid';
        DrawManagement.config.dataUrl = `/backoffice/api_v2/draw_management/borrower/SowCategories?lmrid=${DrawManagement.config.lmrid}`;
        DrawManagement.config.saveLineItemsSuccessMessage = 'Draw Request Submitted!';
        this.initEventHandlers();
    },

    /**
     * Check if borrower has permission for a specific action
     * @param {string} permission - Permission key to check
     * @returns {boolean} - Whether the permission is granted
     */
    hasPermission: function(permission) {
        return DrawManagement.templateSettings[permission] === 1 && DrawManagement.currentTemplateData.status !== 'pending';
    },

    /**
     * Apply permissions to UI elements
     */
    applyPermissions: function() {
        const self = this;

        if (!self.hasPermission('allowBorrowersAddEditCategories')) {
            DrawManagement.elements.$openAddCategoryModalBtn.remove();
            $('.edit-category-btn').remove();
        }

        if (!self.hasPermission('allowBorrowersDeleteCategories')) {
            $('.delete-category-btn').remove();
        }

        if (!self.hasPermission('allowBorrowersAddEditLineItems')) {
            $('.add-line-item-row').remove();
            DrawManagement.elements.$lineItemCategoriesContainer.off('click', '.editable-line-item td:not(:last-child)');
            DrawManagement.elements.$lineItemCategoriesContainer.on('click', '.editable-line-item td:not(:last-child)', function(e) {
                if ($(e.target).closest('.note-container').length && !$(e.target).closest('.description-btn').length
                    && DrawManagement.currentTemplateData.status !== 'pending') {
                    return true;
                }

                e.preventDefault();
                e.stopPropagation();
                return false;
            });
        }

        if (!self.hasPermission('allowBorrowersDeleteLineItems')) {
            $('.col-action').remove();
        }

        if (!self.hasPermission('allowBorrowersAddEditCategories') && !self.hasPermission('allowBorrowersDeleteCategories')) {
            DrawManagement.elements.$saveCatBtn.off('click').on('click', function(e) {
                e.preventDefault();
                toastrNotification('You do not have permission to modify categories.', 'warning');
                return false;
            });
            DrawManagement.elements.$saveCatBtn.remove();
        }

        if (DrawManagement.currentTemplateData.status === 'pending') {
            DrawManagement.elements.$lineItemCategoriesContainer.find('input[type="number"]').prop('disabled', true);
            DrawManagement.elements.$saveLineItemsBtn.remove();
        }

    },

    /**
     * Initialize borrower-specific event handlers
     */
    initEventHandlers: function() {
        const self = this;
        this.initNotesModal();

        DrawManagement.elements.$lineItemCategoriesContainer.on('input', 'input[type="number"]', function() {
            DrawManagement.config.lineItemsModified = true;

            const $input = $(this);
            let value = parseFloat($input.val()) || 0;

            if (value < 0) {
                value = 0;
                $input.val(value);
            }

            // Round to 2 decimal places
            value = Math.round(value * 100) / 100;

            const fieldName = $input.attr('name');
            const $row = $input.closest('tr');
            const cost = parseFloat($row.find('input[name="cost"]').val()) || 0;

            if (fieldName === 'cost') {
                const percent = parseFloat($row.find('input[name="completedPercent"]').val()) || 0;
                const amount = Math.round((percent / 100) * value * 100) / 100;
                $row.find('input[name="completedAmount"]').val(amount);
            }

            if (fieldName === 'completedAmount') {
                if (value > cost) {
                    value = cost;
                    $input.val(value);
                }

                const percentage = cost > 0 ? Math.round((value / cost) * 100) : 0;
                $row.find('input[name="completedPercent"]').val(percentage);
            }
            else if (fieldName === 'completedPercent') {
                if (value > 100) {
                    value = 100;
                    $input.val(value);
                }

                const amount = Math.round((value / 100) * cost * 100) / 100;
                $row.find('input[name="completedAmount"]').val(amount);
            }

            $input.val(value);
        });

        self.initNotesPopover('.note-btn');
    },

    /**
     * Initialize notes modal functionality
     */
    initNotesModal: function() {
        let currentNoteBtn;

        $('#noteModal').on('show.bs.modal', function (event) {
            currentNoteBtn = $(event.relatedTarget);
            const noteText = currentNoteBtn.data('note') || '';
            $('#noteTextarea').val(noteText);
        });

        $('#saveNoteBtn').on('click', function () {
            const updatedNote = $('#noteTextarea').val();
            currentNoteBtn.data('note', updatedNote);
            currentNoteBtn.attr('data-note', updatedNote);
            $('#noteModal').modal('hide');
            DrawManagement.config.lineItemsModified = true;
            DrawManagement.elements.$saveLineItemsBtn.prop('disabled', false);
        });
    },

    initNotesPopover: function(elementSelector) {
        let hoverTimeout;
        let isClickInProgress = false;
        DrawManagement.elements.$lineItemCategoriesContainer
            .on('mousedown', elementSelector, function() {
                isClickInProgress = true;
                clearTimeout(hoverTimeout);
                $(this).closest('.note-container').find('.popover').stop(true, true).hide();
            })
            .on('mouseup', elementSelector, function() {
                setTimeout(() => {
                    isClickInProgress = false;
                }, 0);
            })
            .on('click', elementSelector, function(e) {
                $(this).closest('.note-container').find('.popover').stop(true, true).hide();
            })
            .on('mouseover', elementSelector, function() {
                if (isClickInProgress) {
                    return;
                }

                const $btn = $(this);
                const $notesContainer = $btn.closest('.note-container');
                const $popover = $notesContainer.find('.popover');
                clearTimeout(hoverTimeout);

                hoverTimeout = setTimeout(function() {
                    if (isClickInProgress) {
                        $popover.stop(true, true).hide();
                        return;
                    }

                    const note = $btn.data('note');
                    if (note && note.trim()) {
                        $('.popover', DrawManagement.elements.$lineItemCategoriesContainer).not($popover).stop(true, true).hide();
                        $popover.text(note);
                        $popover.stop(true, true).fadeIn(100);
                    } else {
                        $popover.stop(true, true).hide();
                    }
            }, 500);
        })
        .on('mouseleave', elementSelector, function() {
            clearTimeout(hoverTimeout);
            isClickInProgress = false;
            $(this).closest('.note-container').find('.popover').stop(true, true).fadeOut(100);
        });
    },

    /**
     * Build line items JSON for saving
     */
    buildLineItemsJson: function() {
        const groupedLineItems = {};

        DrawManagement.elements.$lineItemCategoriesContainer.find('.line-item-category-section').each(function() {
            const $categoryCard = $(this);
            const categoryId = $categoryCard.data('category-id');
            if (!categoryId) {
                return;
            }

            const itemsForThisCategory = [];
            $categoryCard.find('tbody.sortable tr.editable-line-item').each(function(index) {
                const $row = $(this);
                let lineItemId = $row.data('line-item-id');
                let name, description, cost, completedAmount, completedPercent, notes;

                if (typeof lineItemId === 'string' && lineItemId.startsWith('new_li_')) {
                    name = $row.find('.line-item-name-input').val().trim() || $row.data('new-name');
                    description = '';
                    if (!name) {
                        return;
                    }
                    lineItemId = null;
                } else {
                    name = $row.find('.line-item-name-display').text().trim();
                    lineItemId = parseInt(lineItemId, 10);
                }

                cost = parseFloat($row.find('input[name="cost"]').val()) || 0;
                completedAmount = parseFloat($row.find('input[name="completedAmount"]').val()) || 0;
                completedPercent = parseFloat($row.find('input[name="completedPercent"]').val()) || 0;
                notes = $row.find('.note-btn:not(.description-btn)').data('note') || '';
                description = $row.find('.description-btn').data('note') || '';

                itemsForThisCategory.push({
                    id: lineItemId,
                    categoryId: categoryId,
                    name: name,
                    description: description,
                    cost: cost,
                    completedAmount: completedAmount,
                    completedPercent: completedPercent,
                    notes: notes,
                    order: index + 1
                });
            });
            if (itemsForThisCategory.length === 0) {
                groupedLineItems[categoryId] = [];
            }
            groupedLineItems[categoryId] = itemsForThisCategory;
        });

        return {
            lmrid: DrawManagement.config.lmrid,
            lineItems: groupedLineItems,
            isDraft: DrawManagement.config.isDraft
        };
    },

    renderLineItemRowsUI: function(lineItems, $tbodyElement) {
        const rowTemplate = document.getElementById('line-item-row-template');
        if (!rowTemplate) {
            console.error("line-item-row-template not found!");
            return;
        }

        if (lineItems && lineItems.length > 0) {
            lineItems.forEach(item => {
                const clone = rowTemplate.content.cloneNode(true);
                const $row = $(clone).find('tr');
                $row.attr('data-line-item-id', item.id);

                $row.find('.line-item-name-display').text(item.name);
                if(DrawManagement.currentTemplateData.status === 'rejected' && item.rejectReason !== ''){
                    $row.find('.line-item-name-display').append('<i class="fa fa-info-circle text-danger ml-2"></i>');
                }
                $row.find('.line-item-name-input').val(item.name);
                $row.find('.line-item-name-display').attr('title', item.description);
                $row.find('.line-item-description-input').val(item.description);

                $row.find('input[name="cost"]').val(item.cost || 0);
                $row.find('input[name="completedAmount"]').val(item.completedAmount || 0);
                $row.find('input[name="completedPercent"]').val(item.completedPercent || 0);

                const popoverElems = $row.find('.note-btn:not(.lender-notes)');

                popoverElems.each(function() {
                    const $popoverElem = $(this);
                    const isDescriptionBtn = $popoverElem.hasClass('description-btn');
                    const note = isDescriptionBtn ? item.description : item.notes;
                    if (note) {
                        $popoverElem.data('note', note);
                        $popoverElem.attr('data-note', note);
                    } else {
                        $popoverElem.data('note', '');
                    }
                });

                let lenderNotes = item.rejectReason !== '' ? item.lenderNotes + '\n\nRejection Reason:\n' + item.rejectReason : item.lenderNotes;
                $row.find('.lender-notes').data('note', lenderNotes.trim());
                $row.find('.lender-notes').attr('data-note', lenderNotes.trim());

                $tbodyElement.append($row);
            });
        }
    }
};

