<?php

namespace pages\backoffice\api_v2\draw_management\borrower\SowLineItems;

use models\portals\BackofficePage;
use models\standard\HTTP;
use models\cypher;
use models\composite\oDrawManagement\DrawRequestManager;

/**
 * Class SowLineItems
 *
 * API endpoint for updating and fetching SOW line items
 *
 * @package pages\backoffice\api_v2\draw_management
 */
class SowLineItems extends BackofficePage
{
    /**
     * Handle POST requests to update SOW line items
     *
     * @return void
     */
    public static function Post(): void
    {
        parent::Init();
        $postData = file_get_contents("php://input");
        $postData = json_decode($postData, true);

        $LMRId = $postData['lmrid'];
        if ($LMRId && !is_numeric($LMRId)) $LMRId = cypher::myDecryption($LMRId);
        $LMRId = (int)$LMRId;

        if (!$LMRId) {
            HTTP::ExitJSON(["success" => false, "message" => "LMRId is required."]);
            return;
        }

        try {
            if (!isset($postData['lineItems'])) {
                HTTP::ExitJSON(["success" => false, "message" => "Invalid request data."]);
                return;
            }

            $lineItemsData = $postData['lineItems'];
            $isDraft = $postData['isDraft'] ?? false;
            $drawRequestManager = DrawRequestManager::forLoanFile($LMRId);
            $success = $drawRequestManager->saveLineItems($lineItemsData, $isDraft);
            if (!$success) {
                HTTP::ExitJSON(["success" => false, "message" => "Failed to save line items."]);
                return;
            }
            $lineItemsData = $drawRequestManager->getDrawRequestDataArray();
            HTTP::ExitJSON(["success" => true, "message" => "Line items saved successfully.", "data" => $lineItemsData]);

        } catch (\Exception $e) {
            HTTP::ExitJSON(["success" => false, "message" => "An error occurred: " . $e->getMessage()]);
        }
    }
}
