<?php
namespace models\composite\oDrawManagement;

use models\types\strongType;
use models\cypher;
use models\lendingwise\tblFile;
use models\lendingwise\tblDrawRequests;
use models\lendingwise\db\tblDrawRequests_db;
use models\composite\oDrawManagement\BorrowerDrawRequest;
use models\composite\oDrawManagement\BorrowerDrawCategory;
use models\composite\oDrawManagement\BorrowerDrawLineItem;
use models\composite\oDrawManagement\SowTemplateManager;
use models\Database2;

class DrawRequestManager extends strongType
{
    /**
     * @var int|null The LMR ID associated with the draw request manager.
     */
    private ?int $LMRId = null;

    /**
     * @var int|null The ID of the borrower draw request.
     */
    private ?int $drawRequestId = null;

    /**
     * @var BorrowerDrawRequest|null The borrower draw request object.
     */
    private ?BorrowerDrawRequest $drawRequest = null;

    /**
     * @var int The maximum number of categories allowed.
     */
    public const MAX_CATEGORIES = 20;

    /**
     * DrawRequestManager constructor.
     * @param int $LMRId The LMR ID to initialize the manager with.
     */
    public function __construct($LMRId)
    {
        $this->LMRId = $LMRId;
        $this->loadDrawRequest();
    }

    /**
     * Static factory method to create an instance of DrawRequestManager.
     * @param int|null $LMRId The LMR ID to initialize the manager with.
     * @return DrawRequestManager A new instance of DrawRequestManager.
     */
    public static function forLoanFile($LMRId = null): self {
        if ($LMRId === null) {
            throw new \InvalidArgumentException("LMRId must be provided or available in PageVariables.");
        }
        if(!is_numeric($LMRId)) {
            $LMRId = cypher::myDecryption($LMRId);
        }
        return new self($LMRId);
    }

    /**
     * Load or create draw request for the current LMR ID.
     * @return void
     */
    private function loadDrawRequest(): void
    {
        if (!$this->LMRId) {
            return;
        }

        $drawRequestData = tblDrawRequests::Get([tblDrawRequests_db::COLUMN_LMRID => $this->LMRId]);
        if ($drawRequestData) {
            $this->drawRequest = new BorrowerDrawRequest($drawRequestData);
            $this->drawRequestId = $this->drawRequest->id;
        } else {
            $this->drawRequest = $this->getOrCreateDrawRequest();
        }
    }

    /**
     * Get or create a draw request for the current LMR ID.
     * @return BorrowerDrawRequest The draw request object.
     */
    public function getOrCreateDrawRequest(): BorrowerDrawRequest
    {
        if (is_null($this->drawRequest)) {
            // Create a new draw request
            $this->drawRequest = new BorrowerDrawRequest();
            $drawRequestData = [
                'LMRId' => $this->LMRId,
                'status' => BorrowerDrawRequest::STATUS_NEW
            ];
            $this->drawRequest->save($drawRequestData);
            $this->drawRequestId = $this->drawRequest->id;
            $this->copyProcessingCompanyTemplateData();
        }
        return $this->drawRequest;
    }

    /**
     * Public getter for drawRequestId.
     * @return int|null The draw request ID.
     */
    public function getDrawRequestId(): ?int
    {
        return $this->drawRequestId;
    }

    /**
     * Retrieves the BorrowerDrawRequest object.
     * @return BorrowerDrawRequest|null The borrower draw request object.
     */
    public function getDrawRequest(): ?BorrowerDrawRequest
    {
        return $this->drawRequest;
    }

    /**
     * Check if a draw request exists for the current LMR ID.
     * @return bool True if draw request exists, false otherwise.
     */
    public function hasDrawRequest(): bool
    {
        return !is_null($this->drawRequest);
    }

    /**
     * Retrieves the draw request data as an array.
     * If no borrower draw request exists, returns empty array.
     * If borrower draw request exists, returns draw request data.
     * @return array An array containing the draw request data.
     */
    public function getDrawRequestDataArray(): array
    {
        if ($this->hasDrawRequest()) {
            return $this->drawRequest->toArray();
        }

        return [];
    }

    /**
     * Copy processing company template data to the borrower draw request for a new request.
     * This function copies template categories and line items to borrower tables,
     * resetting IDs for categories and using new category IDs for line items.
     * @return void
     */
    private function copyProcessingCompanyTemplateData(): void
    {
        if (!$this->drawRequest || !$this->drawRequest->id) {
            return;
        }

        $templateData = $this->getTemplateDataArray();

        if (empty($templateData['categories'])) {
            return;
        }

        $db = Database2::getInstance();
        $db->beginTransaction();

        try {
            $this->copyTemplateCategoriesAndLineItems($templateData['categories']);
            $db->commit();
            $this->loadDrawRequest();
        } catch (\Exception $e) {
            if($this->hasDrawRequest()) $this->drawRequest->delete();
            $db->rollBack();
            throw $e;
        }
    }

    /**
     * Copy template categories and line items to borrower tables.
     * @param array $templateCategoriesData Array of template categories with line items.
     * @return void
     */
    private function copyTemplateCategoriesAndLineItems(array $templateCategoriesData): void
    {
        foreach ($templateCategoriesData as $templateCategory) {
            // Create new borrower category from template data
            $borrowerCategory = new BorrowerDrawCategory();

            $categoryData = [
                'id' => null,
                'drawId' => $this->drawRequest->id,
                'name' => $templateCategory['name'],
                'description' => $templateCategory['description'] ?? '',
                'order' => $templateCategory['order']
            ];

            $borrowerCategory->save($categoryData);
            $newCategoryId = $borrowerCategory->id;

            // Copy line items for this category
            if (!empty($templateCategory['lineItems'])) {
                $this->copyTemplateLineItems($templateCategory['lineItems'], $newCategoryId);
            }
        }
    }

    /**
     * Copy template line items to borrower line items table.
     * @param array $templateLineItems Array of template line items.
     * @param int $newCategoryId The new category ID to associate line items with.
     * @return void
     */
    private function copyTemplateLineItems(array $templateLineItems, int $newCategoryId): void
    {
        foreach ($templateLineItems as $templateLineItem) {
            $borrowerLineItem = new BorrowerDrawLineItem();

            $lineItemData = [
                'id' => null,
                'drawId' => $this->drawRequest->id,
                'categoryId' => $newCategoryId,
                'name' => $templateLineItem['name'],
                'description' => $templateLineItem['description'] ?? '',
                'order' => $templateLineItem['order'],
                'cost' => 0.00,
                'completedAmount' => 0.00,
                'completedPercent' => 0.00,
                'notes' => ''
            ];

            $borrowerLineItem->save($lineItemData);
        }
    }

    /**
     * Get categories data from template (fallback when no borrower draw request exists).
     * @return array An array containing the template categories data.
     */
    private function getTemplateDataArray(): array
    {
        try {
            // Get PCID from LMRId (you may need to implement this based on your database structure)
            $pcId = self::pcIdFromLMRId($this->LMRId);
            if (!$pcId) {
                return [];
            }

            $sowTemplateManager = SowTemplateManager::forProcessingCompany($pcId);
            return $sowTemplateManager->getTemplateDataArray();
        } catch (\Exception $e) {
            return [];
        }
    }

    /**
     * Save categories with transaction handling and validation.
     * This method processes and saves categories, ensuring they are associated with the correct draw request.
     * It also handles the deletion of categories that are no longer present in the provided data.
     * @param array $categoriesData Array of category data.
     * @return bool True on success, False on failure.
     * @throws \Exception On invalid category IDs or database errors.
     */
    public function saveCategories(array $categoriesData): bool
    {
        if (count($categoriesData) > self::MAX_CATEGORIES) {
            return false;
        }

        $db = Database2::getInstance();
        $db->beginTransaction();
        try {
            // Ensure we have a draw request
            $drawRequest = $this->getOrCreateDrawRequest();
            // Get existing category IDs
            $existingCategories = $drawRequest->getAllCategories();
            $existingCategoryIds = array_column($existingCategories, 'id');
            $updatedCategoryIds = [];

            foreach ($categoriesData as $catData) {
                $id = $catData['id'];
                $catData['drawId'] = $drawRequest->id;

                if ($id) {
                    $categoryObject = $this->getCategoryById($id);
                } else {
                    $catData['id'] = null;
                    $categoryObject = new BorrowerDrawCategory();
                }
                $categoryObject->save($catData);
                $updatedCategoryIds[] = $categoryObject->id;
            }

            $idsToDelete = array_diff($existingCategoryIds, $updatedCategoryIds);
            $this->deleteCategories($idsToDelete);

            $db->commit();
            $this->loadDrawRequest();
            return true;

        } catch (\Exception $e) {
            $db->rollBack();
            return false;
        }
    }

    /**
     * Save line items with transaction handling and validation.
     * This method processes and saves line items, ensuring they are associated with the correct categories and draw request.
     * It also handles the deletion of line items that are no longer present in the provided data.
     * @param array $lineItemsData Grouped line items data format: `['categoryId' => [['id', 'name', 'cost', ...], ...]]`
     * @return bool True on success, False on failure.
     * @throws \Exception On invalid category IDs or database errors.
     */
    public function saveLineItems(array $lineItemsData, bool $isDraft = false): bool
    {
        $db = Database2::getInstance();
        $db->beginTransaction();
        try {
            if (count($lineItemsData) > self::MAX_CATEGORIES) {
                return false;
            }

            // Ensure we have a draw request
            $drawRequest = $this->getOrCreateDrawRequest();

            foreach ($lineItemsData as $categoryId => $lineItems) {
                $borrowerCategory = $this->getCategoryById($categoryId);
                $existingLineItemIds = $borrowerCategory ? array_column($borrowerCategory->getAllLineItems(), 'id') : [];
                $updatedLineItemIds = [];

                foreach ($lineItems as $liData) {
                    $id = $liData['id'] ?? null;
                    $categoryId = $liData['categoryId'];
                    $liData['drawId'] = $drawRequest->id;
                    if ($id) {
                        $lineItemObject = $borrowerCategory->getLineItemById($id);
                    } else {
                        $liData['id'] = null;
                        $lineItemObject = new BorrowerDrawLineItem();
                    }
                    $lineItemObject->save($liData);
                    $updatedLineItemIds[] = $lineItemObject->id;
                }

                $idsToDelete = array_diff($existingLineItemIds, $updatedLineItemIds);
                $this->drawRequest->deleteLineItems($idsToDelete);
            }

            $this->saveScopeOfWorkData($isDraft);
            $this->loadDrawRequest();

            $db->commit();
            return true;

        } catch (\Exception $e) {
            $db->rollBack();
            return false;
        }
    }

    /**
     * Submit the scope of work for the draw request.
     * @param bool $isDraft Whether the submission is a draft.
     * @return bool True on success, False on failure.
     */

    public function saveScopeOfWorkData(bool $isDraft): bool
    {
        $status = BorrowerDrawRequest::STATUS_PENDING;
        if ($isDraft) {
            $status = BorrowerDrawRequest::STATUS_NEW;
        }
        $sowApproved = 0;
        $isDrawRequest = 0;
        return $this->drawRequest->updateDrawRequestStatus($status, $sowApproved, $isDrawRequest);
    }

    /**
     * Get a category by its ID.
     * @param int $categoryId The category ID.
     * @return BorrowerDrawCategory|null The category object or null if not found.
     */
    private function getCategoryById(int $categoryId): ?BorrowerDrawCategory
    {
        if (!$this->drawRequest) {
            return null;
        }

        $categories = $this->drawRequest->getAllCategories();
        foreach ($categories as $category) {
            if ($category->id == $categoryId) {
                return $category;
            }
        }
        return null;
    }

    /**
     * Get a line item by its ID.
     * @param int $lineItemId The line item ID.
     * @return BorrowerDrawLineItem|null The line item object or null if not found.
     */
    public function getLineItemById(int $lineItemId): ?BorrowerDrawLineItem
    {
        if (!$this->drawRequest) {
            return null;
        }

        $allLineItems = $this->drawRequest->getAllLineItems();
        foreach ($allLineItems as $lineItem) {
            if ($lineItem->id == $lineItemId) {
                return $lineItem;
            }
        }
        return null;
    }

    public function saveDrawRequestData(array $postData): bool
    {
        if (!$this->drawRequest || empty($postData['lineItems'])) {
            return false;
        }

        $db = Database2::getInstance();
        $db->beginTransaction();
        try {
            $status = $postData['status'];
            $sowApproved = $this->drawRequest->sowApproved;
            $isDrawRequest = $this->drawRequest->isDrawRequest;
            if ($status === BorrowerDrawRequest::STATUS_APPROVED) {
                $sowApproved = 1;
                $isDrawRequest = $this->drawRequest->sowApproved;
            }
            $this->drawRequest->updateDrawRequestStatus($status, $sowApproved, $isDrawRequest);

            if (isset($postData['lineItems']) && is_array($postData['lineItems'])) {
                foreach ($postData['lineItems'] as $lineItemId => $lineItemData) {
                    $lineItem = $this->getLineItemById((int)$lineItemId);
                    $lineItemDbObj = $lineItem->getDbObject();
                    $lineItemDbObj->lenderNotes = $lineItemData['lenderNotes'] ?? '';
                    $lineItemDbObj->rejectReason = $lineItemData['rejectReason'] ?? '';
                    $lineItemDbObj->save();
                }
            }

            $db->commit();
            $this->loadDrawRequest();
            return true;
        } catch (\Exception $e) {
            $db->rollBack();
            return false;
        }
    }

    /**
     * Delete categories by their IDs.
     * @param array $categoryIds Array of category IDs to delete.
     * @return void
     */
    private function deleteCategories(array $categoryIds): void
    {
        foreach ($categoryIds as $categoryId) {
            $category = $this->getCategoryById($categoryId);
            if ($category) {
                $category->delete();
            }
        }
    }

    /**
     * Get PCID from LMRId.
     * @param int $LMRId The LMR ID.
     * @return int|null The PCID or null if not found.
     */
    public static function pcIdFromLMRId(int $LMRId): ?int
    {
        $fileData = tblFile::Get(['LMRId' => $LMRId]);
        return $fileData ? $fileData->FPCID : null;
    }
}
