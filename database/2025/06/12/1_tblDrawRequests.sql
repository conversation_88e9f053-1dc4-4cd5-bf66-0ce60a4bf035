CREATE TABLE `tblDrawRequests` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `LMRId` INT UNSIGNED NOT NULL,
    `status` ENUM('new', 'pending', 'approved', 'rejected') NOT NULL DEFAULT 'new',
    `sowApproved` TINYINT(1) NOT NULL DEFAULT 0,
    `isDrawRequest` TINYINT(1) NOT NULL DEFAULT 0,
    `submittedAt` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updatedAt` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    CONSTRAINT FK_BorrowerDraws_File FOREIGN KEY (LMRId)
        REFERENCES tblFile(LMRId) ON DELETE CASCADE
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_520_ci;
